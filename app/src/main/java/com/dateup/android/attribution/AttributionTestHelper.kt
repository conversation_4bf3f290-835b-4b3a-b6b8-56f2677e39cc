package com.dateup.android.attribution

import android.content.Context
import com.dateup.android.AccountPreferences
import com.dateup.android.analytics.GOOGLE_ADS
import com.dateup.android.analytics.META_ADS
import com.dateup.android.analytics.ORGANIC
import timber.log.Timber

/**
 * Helper class for testing attribution functionality
 * This should only be used in debug builds for testing purposes
 */
object AttributionTestHelper {

    private const val TAG = "AttributionTestHelper"

    /**
     * Simulate Google Ads attribution for testing
     */
    fun simulateGoogleAdsAttribution(context: Context) {
        if (com.dateup.android.BuildConfig.DEBUG) {
            val prefs = AccountPreferences.getInstance(context)
            prefs.setValue("ad_source", GOOGLE_ADS)
            prefs.setValue("attribution_detected", true)
            Timber.tag(TAG).d("Simulated Google Ads attribution")
        }
    }

    /**
     * Simulate Meta Ads attribution for testing
     */
    fun simulateMetaAdsAttribution(context: Context) {
        if (com.dateup.android.BuildConfig.DEBUG) {
            val prefs = AccountPreferences.getInstance(context)
            prefs.setValue("ad_source", META_ADS)
            prefs.setValue("attribution_detected", true)
            Timber.tag(TAG).d("Simulated Meta Ads attribution")
        }
    }

    /**
     * Simulate organic attribution for testing
     */
    fun simulateOrganicAttribution(context: Context) {
        if (com.dateup.android.BuildConfig.DEBUG) {
            val prefs = AccountPreferences.getInstance(context)
            prefs.setValue("ad_source", ORGANIC)
            prefs.setValue("attribution_detected", true)
            Timber.tag(TAG).d("Simulated organic attribution")
        }
    }

    /**
     * Reset attribution for testing
     */
    fun resetAttribution(context: Context) {
        if (com.dateup.android.BuildConfig.DEBUG) {
            val prefs = AccountPreferences.getInstance(context)
            prefs.setValue("ad_source", "")
            prefs.setValue("attribution_detected", false)
            Timber.tag(TAG).d("Reset attribution")
        }
    }

    /**
     * Get current attribution status for debugging
     */
    fun getAttributionStatus(context: Context): String {
        val prefs = AccountPreferences.getInstance(context)
        val source = prefs.getStringValue("ad_source", "none")
        val detected = prefs.getBooleanValue("attribution_detected", false)
        return "Source: $source, Detected: $detected"
    }

    /**
     * Log current attribution status
     */
    fun logAttributionStatus(context: Context) {
        if (com.dateup.android.BuildConfig.DEBUG) {
            val status = getAttributionStatus(context)
            Timber.tag(TAG).d("Attribution Status: $status")
        }
    }
}
