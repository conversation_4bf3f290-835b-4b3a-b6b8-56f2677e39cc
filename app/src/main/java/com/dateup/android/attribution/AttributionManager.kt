package com.dateup.android.attribution

import android.content.Context
import android.os.RemoteException
import com.android.installreferrer.api.InstallReferrerClient
import com.android.installreferrer.api.InstallReferrerStateListener
import com.android.installreferrer.api.ReferrerDetails
import com.dateup.android.AccountPreferences
import com.dateup.android.analytics.GOOGLE_ADS
import com.dateup.android.analytics.META_ADS
import com.dateup.android.analytics.ORGANIC
import com.dateup.android.utils.Constants
import com.facebook.applinks.AppLinkData
import timber.log.Timber

/**
 * Centralized attribution manager for tracking install sources from Google Ads and Meta Ads
 */
class AttributionManager private constructor() {

    companion object {
        private const val TAG = "AttributionManager"
        private const val AD_SOURCE_KEY = "ad_source"
        private const val ATTRIBUTION_DETECTED_KEY = "attribution_detected"
        
        @Volatile
        private var INSTANCE: AttributionManager? = null
        
        fun getInstance(): AttributionManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: AttributionManager().also { INSTANCE = it }
            }
        }
    }

    private var installReferrerClient: InstallReferrerClient? = null
    private var attributionDetected = false

    /**
     * Initialize attribution tracking - should be called early in Application.onCreate()
     */
    fun initializeAttribution(context: Context) {
        Timber.tag(TAG).d("Initializing attribution tracking")
        
        // Check if attribution was already detected in a previous session
        val prefs = AccountPreferences.getInstance(context)
        attributionDetected = prefs.getBooleanValue(ATTRIBUTION_DETECTED_KEY, false)
        
        if (!attributionDetected) {
            // Try Google Play Install Referrer first
            initializeGooglePlayInstallReferrer(context)
            
            // Try Meta Ads deferred deep linking
            initializeMetaAdsAttribution(context)
        } else {
            Timber.tag(TAG).d("Attribution already detected in previous session")
        }
    }

    /**
     * Initialize Google Play Install Referrer API for Google Ads attribution
     */
    private fun initializeGooglePlayInstallReferrer(context: Context) {
        try {
            installReferrerClient = InstallReferrerClient.newBuilder(context).build()
            installReferrerClient?.startConnection(object : InstallReferrerStateListener {
                override fun onInstallReferrerSetupFinished(responseCode: Int) {
                    when (responseCode) {
                        InstallReferrerClient.InstallReferrerResponse.OK -> {
                            handleInstallReferrerResponse(context)
                        }
                        InstallReferrerClient.InstallReferrerResponse.FEATURE_NOT_SUPPORTED -> {
                            Timber.tag(TAG).w("Install Referrer API not supported")
                            fallbackToOrganic(context)
                        }
                        InstallReferrerClient.InstallReferrerResponse.SERVICE_UNAVAILABLE -> {
                            Timber.tag(TAG).w("Install Referrer service unavailable")
                            // Retry after delay
                            retryInstallReferrer(context)
                        }
                        else -> {
                            Timber.tag(TAG).e("Install Referrer setup failed with code: $responseCode")
                            fallbackToOrganic(context)
                        }
                    }
                }

                override fun onInstallReferrerServiceDisconnected() {
                    Timber.tag(TAG).d("Install Referrer service disconnected")
                    // Will retry on next app launch if attribution not detected
                }
            })
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "Error initializing Install Referrer")
            fallbackToOrganic(context)
        }
    }

    /**
     * Handle successful Install Referrer response
     */
    private fun handleInstallReferrerResponse(context: Context) {
        try {
            val response: ReferrerDetails? = installReferrerClient?.installReferrer
            response?.let { details ->
                val referrerUrl = details.installReferrer
                Timber.tag(TAG).d("Install referrer: $referrerUrl")
                
                if (isGoogleAdsReferrer(referrerUrl)) {
                    setAttributionSource(context, GOOGLE_ADS)
                    Timber.tag(TAG).i("Detected Google Ads install")
                } else {
                    Timber.tag(TAG).d("Install referrer not from Google Ads")
                    // Don't set as organic yet, wait for Meta Ads check
                }
            }
        } catch (e: RemoteException) {
            Timber.tag(TAG).e(e, "Error getting install referrer")
        } finally {
            try {
                installReferrerClient?.endConnection()
            } catch (e: Exception) {
                Timber.tag(TAG).e(e, "Error ending install referrer connection")
            }
        }
    }

    /**
     * Check if referrer URL indicates Google Ads source
     */
    private fun isGoogleAdsReferrer(referrerUrl: String?): Boolean {
        if (referrerUrl.isNullOrEmpty()) return false
        
        // Google Ads referrers typically contain these parameters
        val googleAdsIndicators = listOf(
            "utm_source=google",
            "utm_medium=cpc",
            "gclid=",
            "utm_campaign="
        )
        
        return googleAdsIndicators.any { indicator ->
            referrerUrl.contains(indicator, ignoreCase = true)
        }
    }

    /**
     * Initialize Meta Ads attribution using Facebook SDK
     */
    private fun initializeMetaAdsAttribution(context: Context) {
        try {
            AppLinkData.fetchDeferredAppLinkData(context) { appLinkData ->
                if (appLinkData != null) {
                    val targetUrl = appLinkData.targetUri?.toString()
                    Timber.tag(TAG).d("Meta deferred deep link: $targetUrl")
                    
                    if (isMetaAdsDeepLink(targetUrl)) {
                        setAttributionSource(context, META_ADS)
                        Timber.tag(TAG).i("Detected Meta Ads install")
                    }
                } else {
                    Timber.tag(TAG).d("No Meta deferred deep link found")
                    // If no attribution detected from either source, mark as organic
                    if (!attributionDetected) {
                        fallbackToOrganic(context)
                    }
                }
            }
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "Error initializing Meta Ads attribution")
            if (!attributionDetected) {
                fallbackToOrganic(context)
            }
        }
    }

    /**
     * Check if deep link indicates Meta Ads source
     */
    private fun isMetaAdsDeepLink(targetUrl: String?): Boolean {
        if (targetUrl.isNullOrEmpty()) return false
        
        // Meta Ads deep links typically contain these parameters
        val metaAdsIndicators = listOf(
            "utm_source=facebook",
            "utm_source=instagram", 
            "utm_medium=cpc",
            "utm_medium=social",
            "fbclid=",
            "fb_action_ids=",
            "fb_action_types="
        )
        
        return metaAdsIndicators.any { indicator ->
            targetUrl.contains(indicator, ignoreCase = true)
        }
    }

    /**
     * Retry Install Referrer with exponential backoff
     */
    private fun retryInstallReferrer(context: Context) {
        // Simple retry after 5 seconds
        android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
            if (!attributionDetected) {
                initializeGooglePlayInstallReferrer(context)
            }
        }, 5000)
    }

    /**
     * Set attribution source and mark as detected
     */
    private fun setAttributionSource(context: Context, source: String) {
        val prefs = AccountPreferences.getInstance(context)
        prefs.setValue(AD_SOURCE_KEY, source)
        prefs.setValue(ATTRIBUTION_DETECTED_KEY, true)
        attributionDetected = true
        
        Timber.tag(TAG).i("Attribution source set to: $source")
    }

    /**
     * Fallback to organic attribution
     */
    private fun fallbackToOrganic(context: Context) {
        if (!attributionDetected) {
            setAttributionSource(context, ORGANIC)
        }
    }

    /**
     * Get the stored attribution source
     */
    fun getAttributionSource(context: Context): String {
        return AccountPreferences.getInstance(context)
            .getStringValue(AD_SOURCE_KEY, ORGANIC)
    }

    /**
     * Check if attribution has been detected
     */
    fun isAttributionDetected(context: Context): Boolean {
        return AccountPreferences.getInstance(context)
            .getBooleanValue(ATTRIBUTION_DETECTED_KEY, false)
    }
}
