package com.dateup.android.attribution

import android.content.Context
import android.os.Bundle
import com.android.billingclient.api.Purchase
import com.android.billingclient.api.SkuDetails
import com.dateup.android.analytics.AD_SOURCE
import com.dateup.android.analytics.AnalyticsTrackingService
import com.dateup.android.analytics.FAILURE_REASON
import com.dateup.android.analytics.ONBOARDING_COMPLETE
import com.dateup.android.analytics.PRICE
import com.dateup.android.analytics.RESPONSE_CODE
import com.dateup.android.analytics.SKU
import com.dateup.android.analytics.SUBSCRIPTION_FAILED
import com.dateup.android.analytics.SUBSCRIPTION_SUCCESS
import timber.log.Timber

/**
 * Utility class for attribution-enhanced analytics tracking
 */
object AttributionAnalytics {

    private const val TAG = "AttributionAnalytics"

    /**
     * Log onboarding completion with attribution source
     */
    fun logOnboardingComplete(context: Context) {
        try {
            val bundle = Bundle()
            val attributionSource = AttributionManager.getInstance().getAttributionSource(context)
            bundle.putString(AD_SOURCE, attributionSource)
            
            AnalyticsTrackingService.logEvent(context, ONBOARDING_COMPLETE, bundle)
            Timber.tag(TAG).d("Logged onboarding complete with attribution: $attributionSource")
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "Error logging onboarding complete")
        }
    }

    /**
     * Log subscription success with attribution and purchase details
     */
    fun logSubscriptionSuccess(
        context: Context, 
        purchase: Purchase, 
        skuDetails: SkuDetails? = null
    ) {
        try {
            val bundle = Bundle()
            
            // Add attribution source
            val attributionSource = AttributionManager.getInstance().getAttributionSource(context)
            bundle.putString(AD_SOURCE, attributionSource)
            
            // Add SKU information
            if (purchase.skus.isNotEmpty()) {
                bundle.putString(SKU, purchase.skus[0])
            }
            
            // Add price if available from SkuDetails
            skuDetails?.let { details ->
                bundle.putString(PRICE, details.price)
            }
            
            AnalyticsTrackingService.logEvent(context, SUBSCRIPTION_SUCCESS, bundle)
            Timber.tag(TAG).d("Logged subscription success with attribution: $attributionSource, SKU: ${purchase.skus.firstOrNull()}")
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "Error logging subscription success")
        }
    }

    /**
     * Log any custom event with attribution source automatically added
     */
    fun logEventWithAttribution(
        context: Context,
        eventName: String,
        additionalParams: Bundle? = null
    ) {
        try {
            val bundle = additionalParams ?: Bundle()
            val attributionSource = AttributionManager.getInstance().getAttributionSource(context)
            bundle.putString(AD_SOURCE, attributionSource)
            
            AnalyticsTrackingService.logEvent(context, eventName, bundle)
            Timber.tag(TAG).d("Logged event '$eventName' with attribution: $attributionSource")
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "Error logging event with attribution: $eventName")
        }
    }

    /**
     * Get attribution source for manual use
     */
    fun getAttributionSource(context: Context): String {
        return AttributionManager.getInstance().getAttributionSource(context)
    }
}
