<resources>

    <!-- Project level resources -->
    <string name="app_name">DateUp</string>

    <!-- Alpha -->
    <string name="alpha_group_title">Welcome to the DateUp Alpha Group</string>
    <string name="alpha_group_desc">You\'ve been invited for a sneak peak at DateUp reserved only for friends and family. In exchange for an early look, we\'d love to hear your general feedback as well as any bugs that you encounter.</string>
    <string name="alpha_group_sub_desc">(Don\'t worry about creating the perfect profile or who you match with. We\'ll reset profiles before launch).</string>

    <string name="login_error_desc_generic">Something went wrong, Please try again</string>

    <string name="closed_beta_title">Welcome to the invite-only DateUp private Beta</string>
    <string name="closed_beta_desc">The private beta is currently open in the San Francisco bay area, and in Reno, NV and will be rolling out to many more cities over the next year.</string>

    <!-- Intro1Activity -->
    <string name="intro1_activity_by_proceeding_you_ag_text_view_text">By proceeding you agree to our <a href="https://www.dateup.co/terms-and-conditions">terms of use</a> and <a href="https://www.dateup.co/privacy-policy">privacy policy</a></string>
    <string name="intro1_activity_button_large_active_button_text">Continue with phone number</string>
    <string name="continue_gmail_text">Continue with Gmail</string>

    <!-- PhoneNumberActivity -->
    <string name="phone_number_activity_what_syour_number_text_view_text">What\'s your number?</string>
    <string name="phone_number_activity_we_ll_text_you_acod_text_view_text">We\'ll text you a code to verify your number. Your phone number will not be shared.</string>
    <string name="phone_number_activity_text_view_text_view_text">+1</string>
    <string name="phone_number_activity_e_g5555555555_edit_text_hint">e.g., 5555555555</string>
    <string name="phone_number_activity_button_large_active_button_text">Send Code</string>

    <!-- CodeVerificationActivity -->
    <string name="code_verification_activity_i_didn_tget_acode_text_view_text">I didn’t get a code</string>
    <string name="code_verification_activity_enter6_digit_code_text_view_text">Enter 6-Digit Code</string>
    <string name="code_verification_activity_sent_to_text_view_text">Sent to:\u0020</string>
    <string name="code_verification_activity_button_large_active_button_text">Next</string>
    <string name="delete_action_desc">Enter the code that was just sent to you via SMS to delete your account.</string>
    <string name="delete_action_button_text">Confirm account deletion</string>

    <!-- EmailActivity -->
    <string name="email_activity_button_large_active_button_text">Next</string>
    <string name="email_activity_what_syour_email_text_view_text">What\'s your email?</string>
    <string name="email_activity_this_ensures_that_yo_text_view_text">Never get locked out of your account.</string>
    <string name="email_activity_value_edit_text_hint">e.g., <EMAIL></string>

    <!-- NotificationsActivity -->
    <!-- Facebook -->
    <string name="facebook_app_id">***************</string>
    <string name="fb_login_protocol_scheme">fb1234</string>
    <string name="facebook_client_token">********************************</string>


    <!-- EnableLocationActivity -->
    <string name="enable_location_activity_where_are_you_locate_text_view_text">Where are you located?</string>
    <string name="enable_location_activity_enable_your_location_text_view_text">Enable your location so we can verify DateUp is unlocked in your area.</string>
    <string name="enable_location_activity_button_large_active_button_text">Enable Location</string>

    <!-- SpecifyNeighborhoodActivity -->
    <string name="specify_neighborhood_activity_button_large_active_button_text">Set Location</string>
    <string name="specify_neighborhood_activity_pinch_and_drag_the_mtext_view_text">Pinch and drag the map above to set the neighborhood you live in.</string>
    <string name="specify_neighborhood_activity_only_neighborhood_ntext_view_text">(Only neighborhood name will be shared)</string>
    <string name="specify_neighborhood_activity_set_your_neighborhoo_text_view_text">Set Your Neighborhood</string>

    <!-- FirstNameActivity -->
    <string name="first_name_activity_item_menu_item_text">Back</string>
    <string name="first_name_activity_button_large_active_button_text">Next</string>
    <string name="first_name_activity_what_syour_first_na_text_view_text">What\'s your first name?</string>
    <string name="first_name_activity_value_edit_text_hint">e.g., Anthony or Brittany</string>
    <string name="first_name_activity_this_will_be_display_text_view_text">Please use a valid name (no usernames).</string>

    <!-- DateOfBirthActivity -->
    <string name="date_of_birth_activity_cancel_menu_item_text">Back</string>
    <string name="date_of_birth_activity_button_large_active_button_text">Next</string>
    <string name="date_of_birth_activity_when_were_you_born_text_view_text">When were you born?</string>
    <string name="date_of_birth_activity_mm_edit_text_hint">MM</string>
    <string name="date_of_birth_activity_dd_edit_text_hint">DD</string>
    <string name="date_of_birth_activity_yyyy_edit_text_hint">YYYY</string>
    <string name="dob_confirmation_text">Your birthday is on %1$s. Is that correct? This cannot be changed later.</string>

    <!-- Branding1Activity -->

    <!-- SexPreferenceActivity -->
    <string name="sex_preference_activity_radio_button_unselected_button_text">Man</string>
    <string name="sex_preference_activity_value_text_view_text">Woman</string>
    <string name="sex_preference_activity_both_text_view_text">Both</string>
    <string name="sex_preference_activity_you_are_atext_view_text">You are a…</string>
    <string name="sex_preference_looking_for">Looking for a…</string>
    <string name="sex_preference_activity_you_are_interested_in_text_view_text">You\'re interested in…</string>
    <string name="sex_preference_activity_value_two_text_view_text">Men</string>
    <string name="sex_preference_activity_radio_button_unselected_copy4_button_text">Women</string>
    <string name="sex_preference_activity_button_large_active_button_text">Next</string>

    <!-- YourHeightActivity -->
    <string name="your_height_activity_text_view_text_view_text">5 ft 10 in</string>
    <string name="your_height_activity_how_tall_are_you_text_view_text">How tall are you?</string>
    <string name="your_height_activity_be_as_accurate_as_po_text_view_text">Be as accurate as possible. All heights are welcome, but you will receive less matches if found being dishonest.</string>
    <string name="your_height_activity_button_large_active_button_text">Next</string>

    <!-- HeightCelebrationActivity -->
    <string name="height_celebration_activity_is_agreat_he_text_view_text">%1$s is a great height!</string>
    <string name="height_celebration_activity_we_ll_do_our_best_to_text_view_text">We\'ll do our best to find you a height-compatible match that\'s 100% into you.</string>

    <!-- HeightPreferenceWomenActivity -->

    <!-- BrandingProfileActivity -->
    <string name="branding_profile_activity_a_great_match_begins_text_view_text">A great match begins with a great profile.</string>
    <string name="branding_profile_activity_button_large_active_button_text">Set up your profile</string>

    <!-- Images3Activity -->
    <string name="images3_activity_let_schoose_your_ph_text_view_text">Let\'s choose your photos</string>
    <string name="images3_activity_please_upload_at_lea_text_view_text">Please upload at least two photos of yourself. We recommend at least one photo with nobody else in the picture.</string>
    <string name="images3_activity_button_large_active_button_text">Next</string>

    <!-- ProfessionActivity -->
    <string name="profession_activity_edit_menu_item_text">Skip</string>
    <string name="profession_activity_button_large_active_button_text">Next</string>
    <string name="profession_activity_what_syour_professi_text_view_text">What\'s your profession?</string>
    <string name="profession_activity_value_edit_text_hint">e.g., Kindergarten Teacher</string>

    <!-- Education1Activity -->
    <string name="education1_activity_button_large_active_button_text">Next</string>
    <string name="education1_activity_add_another_text_view_text">+Add Another</string>
    <string name="education1_activity_where_did_you_go_to_text_view_text">Where did you go to school?</string>
    <string name="education1_activity_value_edit_text_hint">e.g. University of Vermont</string>
    <string name="education1_activity_value_edit_text_hint_2">e.g. University of Texas</string>

    <!-- HometownActivity -->
    <string name="hometown_activity_button_large_active_button_text">Next</string>
    <string name="hometown_activity_where_is_your_hometo_text_view_text">Where is your hometown?</string>
    <string name="hometown_activity_value_edit_text_hint">e.g. Chicago</string>

    <!-- Icebreaker1Activity -->
    <string name="icebreaker1_activity_let_sshow_off_your_text_view_text">Choose two profile questions.</string>
    <string name="icebreaker1_activity_choose_two_profile_qtext_view_text">Pro tip: keep them light hearted! They\'re just conversation starters.</string>
    <string name="row_question_view_holder_your_favorite_hobby_text_view_text">Your favorite hobby…</string>

    <!-- Icebreaker2Activity -->
    <string name="icebreaker2_activity_placeholder_edit_text_hint">e.g. I want to go backpacking with Rome with my brothers</string>
    <string name="icebreaker2_activity_field_title_text_view_text">Next vacation I want to go on…</string>
    <string name="icebreaker2_activity_text_view_text_view_text">140/140</string>
    <string name="icebreaker2_activity_button_large_active_button_text">Save</string>

    <!-- Icebreaker3Activity -->
    <string name="icebreaker3_activity_you_re_halfway_there_text_view_text">You\'re almost done!</string>
    <string name="icebreaker3_activity_select_one_more_ques_text_view_text">Select one more question to answer to finish off that amazing profile.</string>
    <string name="row_question_two_two_view_holder_your_favorite_hobby_text_view_text">Your favorite hobby…</string>

    <!-- Icebreaker4Activity -->
    <string name="icebreaker4_activity_placeholder_edit_text_hint">ex: Visit all 50 states</string>
    <string name="icebreaker4_activity_field_title_text_view_text">A life goal of mine…</string>
    <string name="icebreaker4_activity_text_view_text_view_text">140/140</string>

    <!-- BlankOutOfPeopleActivity -->
    <string name="blank_out_of_people_activity_you_re_out_of_people_text_view_text">You’re out of People</string>
    <string name="blank_out_of_people_activity_you_re_out_of_members_text_view_text">You’re out of members</string>
    <string name="blank_out_of_people_activity_you_re_out_of_guests_text_view_text">You’re out of guests</string>
    <string name="blank_out_of_people_activity_try_expanding_your_members_view_text">Try expanding your preferences or switch over to guests.</string>
    <string name="blank_out_of_people_activity_try_expanding_your_guests_view_text">Try expanding your preferences or switch back to members.</string>
    <string name="blank_out_of_people_activity_try_expanding_your_ptext_view_text">Try expanding your preference settings or check back soon.</string>
    <string name="blank_out_of_people_activity_button_large_active_button_text">Open Preferences</string>
    <string name="blank_out_of_people_for_guest">Try expanding your preference settings or check back soon.</string>

    <!-- BlankEnableLocationActivity -->
    <string name="blank_enable_location_activity_we_can_tfind_you_text_view_text">We can’t find you!</string>
    <string name="blank_enable_location_activity_turn_on_location_ser_text_view_text">Turn on location services so that we can match you with someone nearby.</string>

    <!-- BlankTimeForAnUpdateActivity -->
    <string name="blank_time_for_an_update_activity_time_for_an_update_text_view_text">Time for an Update</string>
    <string name="blank_time_for_an_update_activity_this_version_of_date_text_view_text">This version of DateUp is no longer supported. Download the newest version to keep dating up.</string>
    <string name="blank_time_for_an_update_activity_button_large_active_button_text">Download Now</string>

    <!-- BlankNoNetworkConnectionActivity -->
    <string name="blank_no_network_connection_activity_no_network_connectio_text_view_text">No Network Connection</string>
    <string name="blank_no_network_connection_activity_please_make_sure_you_text_view_text">Please make sure you’re connected to the internet.</string>
    <string name="blank_no_network_connection_activity_button_large_active_button_text">Try Again </string>

    <!-- BlankMatchesActivity -->
    <string name="blank_matches_activity_matches_coming_soon_text_view_text">Matches Coming Soon</string>
    <string name="blank_matches_activity_when_you_match_with_text_view_text">Keep browsing — your next match could be just around the corner!</string>
    <string name="blank_matches_chats_title">No Matches Yet</string>
    <string name="blank_matches_chats_requests_title">No Message Requests Yet</string>
    <string name="blank_matches_chats_requests">When someone sends you a message request, it’ll show up here. </string>

    <!-- LikesYouBlankActivity -->
    <string name="blank_likes_you">No likes to show just yet. We\'ll show you all profiles that have liked you here.</string>

    <!-- SameSexPreference2Activity -->
    <string name="same_sex_preference2_activity_cancel_menu_item_text">Back</string>
    <string name="same_sex_preference2_activity_value_text_view_text">Taller</string>
    <string name="same_sex_preference2_activity_radio_button_unselected_copy4_button_text">Shorter</string>
    <string name="same_sex_preference2_activity_should_the_women_be_text_view_text">Should your match be taller or shorter than you?</string>
    <string name="same_sex_preference2_activity_you_can_specify_sho_text_view_text">(You can specify shorter or taller matches for same-sex matchmaking)</string>

    <!-- LocationLockedActivity -->

    <string name="you_are_on_waitlist">You’re on the Waitlist</string>
    <string name="location_locked_activity_dateup_is_not_yet_li_text_view_text">DateUp is currently locked in your area, but we’ll let you know as soon as it’s available.</string>
    <string name="location_locked_activity_we_ll_let_you_know_atext_view_text">In the meantime, share the app with friends to speed up the process:</string>

    <!-- GenderFlow -->
    <string name="more_options"><u>More gender options</u></string>


    <!-- SameSexTallerActivity -->
    <string name="same_sex_taller_activity_how_much_taller_shou_text_view_text">How much taller should your matches be?</string>
    <string name="same_sex_taller_activity_and_up_text_view_text">%1$s and up</string>

    <!-- HeightPreferenceMenActivity -->
    <string name="height_preference_men_activity_to64_text_view_text">%1$s - %2$s</string>
    <string name="height_preference_men_activity_how_tall_should_your_text_view_text">How tall should your matches be?</string>
    <string name="height_preference_men_activity_button_large_active_button_text">Next</string>

    <string name="share_app_link">You\'ve been invited to join DateUp, the dating app that puts tall women first. https://bit.ly/3mI1grL</string>

    <!-- SettingsShorterOrTallerActivity -->

    <!-- SettingsUpdateLocationActivity -->

    <!-- TwoActivity -->

    <!-- ThreeActivity -->


    <!-- SameSexShorterActivity -->
    <string name="same_sex_shorter_activity_text_view_text_view_text">5</string>
    <string name="same_sex_shorter_activity_right_text_view_text">  </string>
    <string name="same_sex_shorter_activity_left_text_view_text"> </string>
    <string name="same_sex_shorter_activity_only_showing_heights_text_view_text">Only showing heights shorter than yourself.</string>
    <string name="same_sex_shorter_activity_choose_amax_height_text_view_text">Choose a max height for your matches.</string>

    <!-- SameSexTallerActivity -->
    <string name="same_sex_taller_activity_text_view_text_view_text">5</string>
    <string name="same_sex_taller_activity_right_text_view_text">  </string>
    <string name="same_sex_taller_activity_left_text_view_text"> </string>
    <string name="same_sex_taller_activity_only_showing_heights_text_view_text">Only showing heights taller than yourself.</string>
    <string name="same_sex_taller_activity_choose_aminimum_hei_text_view_text">Choose a minimum height for your matches.</string>

    <!-- MinHeightForMenActivity -->
    <string name="min_height_for_men_activity_text_view_text_view_text">5</string>
    <string name="min_height_for_men_activity_dateup_only_matches_text_view_text">DateUp only matches you with women at least one inch shorter than yourself.</string>
    <string name="min_height_for_men_activity_choose_aminimum_hei_text_view_text">Choose a minimum height for your matches.</string>

    <!-- MinHeightForWomenActivity -->
    <string name="min_height_for_women_activity_text_view_text_view_text">5</string>
    <string name="min_height_for_women_activity_dateup_only_matches_text_view_text">DateUp only matches you with men at least one inch taller than yourself.</string>
    <string name="min_height_for_women_activity_choose_aminimum_hei_text_view_text">Choose a minimum height for your matches.</string>

    <!-- YourHeightActivity -->
    <string name="your_height_activity_right_text_view_text">  </string>
    <string name="your_height_activity_left_text_view_text"> </string>
    <string name="your_height_sub_title">(measure without shoes on)</string>

    <!-- ProfileMenSEActivity -->
    <string name="profile_men_seactivity_profession_text_view_text">Profession</string>
    <string name="profile_men_seactivity_age_text_view_text">Age</string>
    <string name="profile_men_seactivity_education_text_view_text">Education</string>
    <string name="profile_men_seactivity_hometown_text_view_text">Hometown</string>
    <string name="profile_men_seactivity_you_can_wear_up_to2_text_view_text">You\'ll be shorter than while wearing up to 3-inch heels.</string>
    <string name="profile_men_seactivity_how_do_you_describe_text_view_text">How do you describe yourself…</string>
    <string name="profile_men_seactivity_i_mdefinitely_fun_atext_view_text">Outside. Always outside.</string>
    <string name="profile_men_seactivity_how_do_you_describe_two_text_view_text">How do you describe yourself…</string>
    <string name="profile_men_seactivity_i_mdefinitely_fun_atwo_text_view_text">I’m definitely fun and outgoing! I like to be outside all the time. </string>
    <string name="profile_men_seactivity_button_ghost_button_text">Report</string>

    <!-- SettingsHomeSupernovaActivity -->
    <string name="settings_main_item_1">View or edit profile</string>
    <string name="settings_main_item_2">Settings</string>
    <string name="settings_main_item_3">How it works</string>
    <string name="settings_main_item_4">Contact us</string>
    <string name="settings_main_item_6">Invite others to join DateUp</string>
    <string name="settings_main_item_stub">Account</string>
    <string name="settings_main_item_5">Verify your height</string>

    <!-- SettingsSettingsSupernovaActivity -->
    <string name="settings_settings_item_1">Preferences</string>
    <string name="settings_settings_item_2">Notifications</string>
    <string name="settings_settings_item_3">Account</string>
    <string name="settings_settings_supernova_activity_title_text_view_text">Settings</string>

    <!-- AccountSupernovaActivity -->
    <string name="settings_account_item_1">Update phone number</string>
    <string name="settings_account_item_2">Update email</string>
    <string name="settings_account_item_3">Sign out</string>
    <string name="settings_account_item_4">Delete Account</string>

    <!-- NotificationsSupernovaActivity -->
    <string name="settings_notifications_item_1">New matches</string>
    <string name="settings_notifications_item_2">Messages</string>
    <string name="settings_notifications_item_3">Everything else</string>
    <string name="settings_notifications_title">Notifications</string>

    <!-- PreferencesMenSECopy2supernovaActivity -->
    <string name="preferences_men_secopy2supernova_activity_title_text_view_text">Age range</string>
    <string name="preferences_men_secopy2supernova_activity_value_text_view_text">%1$s - %2$s</string>
    <string name="preferences_men_secopy2supernova_activity_sex_text_view_text">Distance</string>
    <string name="preferences_men_secopy2supernova_activity_value_two_text_view_text">Within %1$s miles</string>
    <string name="preferences_men_secopy2supernova_activity_title_two_text_view_text">Guest height</string>
    <string name="preferences_men_secopy2supernova_activity_value_three_text_view_text">%1$s - %2$s</string>
    <string name="preferences_men_secopy2supernova_activity_sex_preference_text_view_text">SEX PREFERENCE</string>
    <string name="preferences_men_secopy2supernova_activity_sex_preference_two_text_view_text">HEIGHT PREFERENCES</string>
    <string name="preferences_men_secopy2supernova_activity_sex_preference_three_text_view_text">MORE FILTERS</string>
    <string name="preferences_men_secopy2supernova_activity_sex_preference_four_text_view_text">GUESTS</string>
    <string name="preferences_men_secopy2supernova_activity_i_minterested_in_text_view_text">I\'m Interested in</string>
    <string name="preferences_men_secopy2supernova_activity_guest_matchmaking_text_view_text">Guest matchmaking</string>
    <string name="preferences_men_secopy2supernova_activity_guests_are_women_below_text_view_text">Guests are women below 5\'8\" that are seeking tall %1$s.</string>
    <string name="preferences_men_secopy2supernova_activity_guests_are_men_below_text_view_text">Guests are men below 6\'0\" that are seeking tall %1$s.</string>
    <string name="preferences_men_secopy2supernova_activity_sex_two_text_view_text">Height</string>
    <string name="preferences_men_secopy2supernova_activity_value_four_text_view_text">%1$s - %2$s</string>
    <string name="preferences_men_secopy2supernova_activity_title_three_text_view_text">Preferences</string>
    <string name="preferences_teleport_title">TELEPORT MODE</string>
    <string name="preferences_other_unlocked_cities">MY CITIES</string>
    <string name="preferences_teleport_location">Location</string>
    <string name="preferences_teleport_location_desc">Using your current location</string>
    <string name="teleport_desc">Use teleport mode to instantly start browsing in any city that has been unlocked.</string>
    <string name="height_filters_disabled_text">Just a heads up, height filters will be hidden when “Everyone” is selected.</string>


    <!-- HeightCelebration1Activity -->
    <string name="height_celebration1_activity_right_text_view_text">  </string>
    <string name="height_celebration1_activity_left_text_view_text"> </string>
    <string name="height_celebration1_activity_congrats_you_re_amtext_view_text">Congrats, you\'re a member!</string>
    <string name="height_celebration1_activity_men_over61_and_wo_text_view_text">Men over 6\'1&quot; and women over 5\'8&quot; are considered Members.  Users under these heights are considered Guests.</string>
    <string name="height_celebration1_activity_button_large_active_button_text">Next</string>

    <!-- AgeRestrictionActivity -->
    <string name="age_restriction_activity_dateup_is_only_for_ttext_view_text">DateUp is only for those over 18.</string>
    <string name="age_restriction_activity_check_back_after_you_text_view_text">Check back after you’ve turned 18 to use the app.</string>
    <string name="age_restriction_activity_button_large_active_button_text">Email Support</string>
    <string name="age_restriction_activity_right_text_view_text"> </string>
    <string name="age_restriction_activity_left_text_view_text"> </string>

    <!-- GuestMatchmaking2Activity -->
    <string name="guest_matchmaking2_activity_should_we_show_you_gtext_view_text">Should we show you Guests that might be interested in you? You can always update your preferences later.</string>
    <string name="guest_matchmaking2_activity_button_large_active_button_text">No, not interested</string>
    <string name="guest_matchmaking2_activity_button_large_active_two_button_text">Yes, show me guests too</string>
    <string name="guest_matchmaking2_activity_guests_are_men_unde_text_view_text">As a reminder, Guests are men under 6\'0\" or women under 5\'8\" that want to date-up.</string>

    <!-- HeightCelebration3Activity -->
    <string name="height_celebration3_activity_is_agreat_heig_text_view_text">5\'4&quot; is a great height!</string>
    <string name="height_celebration3_activity_let_sproceed_with_stext_view_text">Let\'s proceed with setting up your preferences and find you a tall match.</string>
    <string name="height_celebration3_activity_button_large_active_button_text">Next</string>
    <string name="height_celebration2_activity_men_over61_and_wo_text_view_text">Men over 6\'0\" and women over 5\'8\" are considered Members.  Users under these heights are considered Guests.</string>

    <!-- EditProfile1Activity -->
    <string name="edit_profile1_activity_title_text_view_text">Edit profile</string>
    <string name="edit_profile1_activity_left_text_view_text">Save</string>
    <string name="edit_profile1_activity_right_text_view_text">Cancel</string>
    <string name="edit_profile1_activity_value_edit_text_hint">e.g. Kindergarten Teacher</string>
    <string name="edit_profile1_activity_field_title_text_view_text">Profession</string>
    <string name="edit_profile1_activity_photos_text_view_text">Photos</string>
    <string name="edit_profile1_activity_the_basics_text_view_text">The Basics</string>
    <string name="edit_profile1_activity_questions_text_view_text">Questions</string>
    <string name="edit_profile1_activity_value_two_edit_text_hint">e.g. University of Nevada, Reno</string>
    <string name="edit_profile_education_2_hint">e.g. University of Texas</string>
    <string name="edit_profile1_activity_field_title_two_text_view_text">Education</string>
    <string name="edit_profile1_activity_add_another_text_view_text">+Add Another</string>
    <string name="edit_profile1_activity_value_three_edit_text_hint">e.g. Dallas</string>
    <string name="edit_profile1_activity_field_title_three_text_view_text">Hometown</string>
    <string name="edit_profile1_activity_your_favorite_hobby_text_view_text">Next vacation I want to go on…</string>
    <string name="edit_profile1_activity_your_favorite_hobby_two_text_view_text">Next vacation I want to go on…</string>

    <!--chat-->
    <string name="date_header_today">Today</string>
    <string name="date_header_yesterday">Yesterday</string>

    <!--Upgrade-->
    <string name="likes_you_no">%1$s people have liked you</string>
    <string name="one_person_likes_you_no">1 person has liked you</string>
    <string name="likes_you">Likes you</string>
    <string name="likes_you_desc">Upgrade to DateUp Plus and as others like you, you\'ll see them here. </string>
    <string name="likes_you_button">Upgrade to DateUp Plus</string>

    <!-- age -->
    <string name="age_banned_message">You have been banned from using DateUp as it is only available to those 18 and older.</string>
    <string name="reported_user_banned_message">You have been banned from using DateUp for violating our terms and conditions.</string>
    <string name="okay">Okay</string>
    <string name="login_error">Login Error</string>

    <string name="complete_profile">Complete your profile</string>
    <string name="continue_text">Continue</string>
    <string name="cancel">Cancel</string>
    <string name="complete_profile_desc">Looks like your profile is incomplete. Tap continue to complete it and start sending likes.</string>

    <!-- height confirmation -->
    <string name="height_confirmation_title">You\'ve indicated that you are %1$s. Is that correct?</string>
    <string name="height_confirmation_desc">Be as accurate as possible for the most future matches.</string>
    <string name="height_confirmation_positive_button">Yes, it\'s correct</string>
    <string name="height_confirmation_negative_button">No, edit height</string>

    <!-- abandon -->
    <string name="no_keep_editing">No, keep editing</string>
    <string name="yes_abandon">Yes, abandon</string>
    <string name="abandon_desc">You won\'t be able to like anyone until your profile is finished.</string>
    <string name="abandon_title">Are you sure, you want to abandon your profile? </string>

    <!-- height verification -->
    <string name="height_verification_scan_desc">Verify your height through a quick ID scan to get even more matches. Only date-of-birth and height will be saved.</string>
    <string name="verify_height">Get a Height-Verified Profile</string>
    <string name="scan_now">Scan my license</string>
    <string name="scan_success">Your height has been verified</string>
    <string name="scan_result_desc_success">We detected that your height is %1$s and your age is %2$s. Would you like to accept this information and update your profile?</string>
    <string name="sca_result_desc_failed">You can authorize us to review your license data so that we can manually verify your height (usually within 7 days) or try again in the future.</string>
    <string name="id_not_verified">Sorry, we can\'t auto-detect your height from your license</string>
    <string name="accept_verification">Accept verification</string>
    <string name="cancel_verification">Cancel verification</string>
    <string name="submit_licence">Submit license data</string>
    <string name="cancel_and_try_later">Cancel and try again later</string>
    <string name="height_scan_desc">Point the camera towards the large barcode on the back of your license.</string>
    <string name="height_failed_dob_mismatch_title">Sorry, we aren’t able to verify your height.</string>
    <string name="height_failed_dob_mismatch_desc">Your date-of-birth identified on your license needs to match the date-of-birth that you used when signing up in order to verify your profile.</string>
    <string name="height_scan_failed_title">Sorry, we can’t auto-detect your height from your license</string>
    <string name="height_scan_failed_desc">You can authorize us to review your license data we can see what is going wrong and notify you when to try again.</string>
    <string name="height_scan_failed_alert">Thanks, we’ll notify you when you can try to verify your height again (make sure your notifications are turned on).</string>
    <string name="height_scan_success_alert_title">Congrats, you’re verified</string>
    <string name="height_scan_success_alert_desc">A verification check has been added to your profile.</string>
    <string name="got_it">Got it</string>
    <string name="server_error_generic">Sorry there was a temporary server error. Please try again later</string>
    <string name="height_scan_help_title">If your license is not scanning here are some tips:</string>
    <string name="height_scan_help_point_1">Make sure you have good lighting or turn on the camera flash.</string>
    <string name="height_scan_help_point_2">Ensure your license is not damaged If the barcode is damaged, it may not scan.</string>
    <string name="height_scan_help_point_3">Send us an email to troubleshoot if you’re still having issues.</string>
    <string name="back">Back</string>
    <string name="height_verified">Height-verified</string>
    <string name="verify_your_height">Verify your height</string>

    <string name="how_it_works">How it works</string>
    <string name="how_it_works_point_1">DateUp puts tall women first</string>
    <string name="how_it_works_point_1_desc">DateUp\'s mission is to create the best dating experience for tall women, but people of all heights are welcome.</string>
    <string name="how_it_works_point_2">Tall people join as \"Members\"</string>
    <string name="how_it_works_point_2_desc">Members can easily find and match with other members nearby.</string>
    <string name="how_it_works_point_3">Shorter people join as \"Guests\"</string>
    <string name="how_it_works_point_3_desc">Members can see Guests nearby that are comfortable with dating-up.</string>

    <!-- Strings for camera settings. -->
    <string name="pref_category_camera">Camera</string>
    <string name="pref_key_rear_camera_preview_size" translatable="false">rcpvs</string>
    <string name="pref_key_rear_camera_picture_size" translatable="false">rcpts</string>
    <string name="pref_title_rear_camera_preview_size">Rear camera preview size</string>
    <!-- Strings for product search settings. -->
    <string name="pref_key_enable_auto_search" translatable="false">pkeas</string>
    <string name="pref_key_object_detector_enable_multiple_objects" translatable="false">odemo</string>
    <string name="pref_key_object_detector_enable_classification" translatable="false">odec</string>
    <string name="pref_key_confirmation_time_in_auto_search" translatable="false">ctias</string>
    <string name="pref_key_confirmation_time_in_manual_search" translatable="false">ctims</string>
    <string name="pref_key_enable_barcode_size_check" translatable="false">barcode_ebsc</string>
    <string name="pref_key_minimum_barcode_width" translatable="false">barcode_mbw</string>
    <string name="pref_key_barcode_reticle_width" translatable="false">barcode_brw</string>
    <string name="pref_key_barcode_reticle_height" translatable="false">barcode_brh</string>
    <string name="pref_key_delay_loading_barcode_result" translatable="false">barcode_dlbr</string>


    <!-- Minor subscription notices -->
    <string name="grace_period_message">Something’s wrong with your subscription, but you’ve still got access during a temporary grace period. Tap to fix it.</string>
    <string name="restore_message_with_date">Your subscription is canceled and will expire on %1$s. Tap to restore your subscription.</string>

    <!-- Different account is using subscription -->
    <string name="transfer_message">Wrong taxi? A different account is already using your subscription, but you can transfer your subscription to this account.</string>
    <string name="transfer_message_with_sku">Wrong taxi? A different account is already using your %1$s subscription, but you can transfer your subscription to this account.</string>
    <string name="transfer_message_with_two_skus">Wrong taxi? A different account is already using your %1$s and %2$s subscriptions, but you can transfer your subscriptions to this account.</string>
    <string name="transfer_button_text">Transfer</string>

    <!-- Account hold -->
    <string name="account_hold_message">Classy Taxi is stuck! Your account is on hold because there’s a problem with your payment method. Update your payment method or add a new one to restore your subscription.</string>
    <string name="fix_button_text">Manage payment methods</string>

    <!-- Account paused -->
    <string name="account_paused_message">Classy Taxi is taking a break. Payments for your subscription are paused and will automatically resume on %1$s. You can resume your subscription at any time.</string>
    <string name="account_paused_button_text">Manage subscription</string>

    <!-- Active subscriptions -->
    <string name="premium_message">You have a Premium subscription</string>
    <string name="basic_message">You have a Basic subscription</string>
    <string name="basic_content_text">Welcome to the basic classy taxi, driving through the peaceful cool of the night.</string>
    <string name="premium_content_text">Welcome to the premium space taxi, flying through the endless possibilities of space.</string>

    <!-- Paywall -->
    <string name="paywall_message">Subscribe to a Basic or Premium plan</string>
    <string name="premium_paywall_message">You need to subscribe to a Premium plan</string>

    <!-- Upgrade -->
    <string name="premium_upgrade_message">You currently have a Basic plan. Upgrade to enjoy the benefits of the Premium plan.</string>
    <string name="upgrade_button_text">Upgrade</string>

    <!-- Manage subscriptions -->
    <string name="manage_subscription_label">Manage subscriptions</string>
    <string name="manage_subscription_message">Manage your subscription on Google Play.</string>
    <string name="google_play_subscriptions">Google Play Subscriptions</string>

    <!-- Subscription options -->
    <string name="subscription_options_label">Subscription options</string>
    <string name="basic_button_text">Basic</string>
    <string name="premium_button_text">Premium</string>
    <string name="subscription_option_basic_message">Basic plan</string>
    <string name="subscription_option_basic_message_current">Basic plan (current)</string>
    <string name="subscription_option_basic_message_account_hold">Basic plan (on hold)</string>
    <string name="subscription_option_basic_message_account_paused">Basic plan (paused)</string>
    <string name="subscription_option_basic_message_grace_period">Basic plan (payment issue)</string>
    <string name="subscription_option_basic_message_restore">Basic plan (canceled)</string>
    <string name="subscription_option_premium_message">Premium plan</string>
    <string name="subscription_option_premium_message_current">Premium plan (current)</string>
    <string name="subscription_option_premium_message_account_hold">Premium plan (on hold)</string>
    <string name="subscription_option_premium_message_account_paused">Premium plan (paused)</string>
    <string name="subscription_option_premium_message_grace_period">Premium plan (payment issue)</string>
    <string name="subscription_option_premium_message_restore">Premium plan (canceled)</string>

    <!-- Subscription Screen -->
    <string name="upgrade">upgrade</string>
    <string name="dateup_select">dateupSELECT</string>
    <string name="dateup_plus">dateupPLUS</string>
    <string name="whats_included">What\'s Included</string>
    <string name="select">Select</string>
    <string name="plus">Plus</string>
    <string name="direct_messages_title">3 Direct Messages per Day</string>
    <string name="direct_messages_desc">Message anyone, anytime — no match needed.</string>
    <string name="passed_profiles_title">View Passed Profiles</string>
    <string name="passed_profiles_desc">Explore any profiles you\'ve passed on already.</string>
    <string name="who_likes_you_title">See Who Likes You</string>
    <string name="who_likes_you_desc">Save time by seeing everyone that\'s liked you.</string>
    <string name="teleport_title">Teleport Mode</string>
    <string name="unlimited_likes_title">Unlimited Likes</string>
    <string name="unlimited_likes_desc">Browse without limits until your heart is content.</string>
    <string name="read_receipts_title">Read Receipts</string>
    <string name="read_receipts_desc">Always know when your message has been read.</string>
    <string name="upgrade_to_select">Upgrade to DateUp Select</string>
    <string name="upgrade_to_plus">Upgrade to DateUp Plus</string>
    <string name="restore_subscription">Restore Subscription</string>

    <!-- Influencer offer -->
    <string name="influencer_offer_title">offer courtesy of %1$s</string>
    <string name="subscription_title_influencer_trail">Try DateUp Plus free for 7 days</string>
    <string name="influencer_offer_action_button_title">Start 7-day free trial</string>
    <string name="influencer_offer_pricing_title">$0.00 for 7 days, then $19.99/month</string>
    <string name="influencer_offer_last_chance">Last chance for a free 7-day trial of DateUp Plus</string>

    <!-- Free Regular offer -->
    <string name="subscription_title_free_trail">Try DateUp Plus free for 3 days</string>
    <string name="offer_action_button_title">Start 3-day free trial</string>
    <string name="offer_pricing_title">$0.00 for 3 days, then $19.99/month</string>
    <string name="offer_last_chance">Last chance for a free 3-day trial</string>

    <string name="subscription_all_likes">See who likes you</string>
    <string name="subscription_all_likes_desc">Save time by seeing everyone that’s like you.</string>
    <string name="subscription_teleport">Teleport Mode</string>
    <string name="subscription_teleport_desc">Easily teleport to any city where DateUp is unlocked.</string>
    <string name="subscription_unlimited_likes">Unlimited Likes</string>
    <string name="subscription_unlimited_likes_desc">Browse without limits until your heart is content.</string>
    <string name="subscription_read_receipts">Read Receipts</string>
    <string name="subscription_read_receipts_desc">Always know if someone has read your message.</string>
    <string name="join_dateup_plus">Join DateUp Plus</string>
    <string name="subscription_restore_title"><u>Restore Subscription</u></string>
    <string name="subscription_upgrade_title">Upgrade to DateUp Plus</string>
    <string name="subscription_upgrade_title_select">Upgrade to DateUp Select</string>
    <string name="subscription_weekly_title">Weekly - %1$s</string>
    <string name="subscription_monthly_title">1 month - %1$s</string>
    <string name="subscription_three_months_title">3 months - %1$s</string>
    <string name="subscription_half_yearly_title">6 months - %1$s</string>
    <string name="subscription_yearly_title">Yearly - %1$s</string>
    <string name="limited_offer">Limited Time Offer</string>
    <string name="subscription_terms">Recurring billing. Cancel any time. If you choose to purchase a subscription, payment will be charged to your google id account at the confirmation of purchase. The subscription automatically renews unless it is canceled at least 24 hours before the end of the current period. Your account will be charged for renewal within 24 hours prior to the end fo the current period. You can manage and cancel your subscriptions by going to your Google Play Store account settings after purchase. If you’re offered a free trial, any unused portion of it will be forfeited when you purchase a subscription, where applicable. If you have used the trial before, you will be charged immediately. For more information, please visit our <a href="https://www.dateup.co/privacy-policy">privacy policy</a> and <a href="https://www.dateup.co/terms-and-conditions">terms of use</a></string>
    <string name="offer_title">Exclusive First-Time Offer</string>
    <string name="upgrade_to_dateup_plus"><u>Upgrade to DateUp Plus</u></string>
    <string name="dateup_plus_member">DateUp Plus Member</string>
    <string name="dateup_select_member">DateUp Select Member</string>
    <string name="out_of_likes_subscription">You\'ve used all your likes today. Upgrade to DateUp Plus</string>
    <string name="subscription_celebration_welcome_text">Welcome to the Club!</string>
    <string name="subscription_celebration_desc">As a DateUp Plus member, you now have access to the best DateUp features, giving you the best shot to find the perfect match.</string>
    <string name="subscription_celebration_point_1">See who likes you</string>
    <string name="subscription_celebration_point_2">Teleport to other cities</string>
    <string name="subscription_celebration_point_3">Unlimited likes</string>
    <string name="influencer_offer_secondary_button">No thanks</string>

    <string name="pledge_desc_for_men">DateUp was created with the goal of creating the best dating experience for tall women.\n\n
To help accomplish this goal,  please don\'t focus conversations on height. Tall women on DateUp would much rather focus on getting to know you instead of discussing a height difference 🙂 \n\n
Also, please don\'t bring up any height fetishes.</string>
    <string name="pledge_desc_for_women">DateUp was created with the goal of creating the best dating experience for tall women, like you.\n\n
To help accomplish this goal, we\'ve asked shorter men to focus conversations on topics other than height. In return, we ask you to treat all men on DateUp with respect, regardless of height.</string>

    <!-- Image Moderation -->
    <string name="image_moderation_title">Choose a different photo</string>
    <string name="image_moderation_desc">We detected that this photo may not meet our <a href="https://www.dateup.co/photo-guidelines">photo guidelines</a>. You must be present in all photos and explicit content is not allowed, which are common reasons why photos get flagged.</string>
    <string name="image_moderation_close">Close</string>

    <string name="browsing_in">You are browsing in:</string>
    <string name="add_new_city"><u>Add New City</u></string>

    <!-- Purchase state -->
    <string name="purchase_success">Purchase successful</string>
    <string name="purchase_pending">Purchase pending</string>
    <string name="purchase_unspecified">We are having technical difficulties processing your order. Please try again later.</string>

    <string name="quick_tips_count">Quick tips (%1$s/%2$s)</string>

    <!-- review -->
    <string name="review_enjoying_dateup_title">Enjoying DateUp?</string>
    <string name="review_enjoying_dateup_desc">We\'d love to know what you think.</string>
    <string name="review_title">Review DateUp</string>
    <string name="review_desc">Your reviews keep our small team motivated to make DateUp even better.</string>
    <string name="review_give_feedback">We\'d love if you could drop us a line and let us know how DateUp can improve. We extremely value your feedback.</string>
    <string name="review_button_write_review">Write a review</string>
    <string name="review_button_leave_feedback">Leave us feedback</string>

</resources>